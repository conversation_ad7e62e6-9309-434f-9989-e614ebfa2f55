/**
 * إدارة الإعدادات - تكنوفلاش
 */

let currentSettings = {};

/**
 * اختبار وإصلاح مشاكل تحميل الإعدادات
 */
function debugSettingsLoad() {
    console.log('🔍 بدء تشخيص مشاكل تحميل الإعدادات...');

    // فحص localStorage
    try {
        const storedData = localStorage.getItem('technoflash_settings');
        console.log('📦 بيانات localStorage:', storedData ? 'موجودة' : 'غير موجودة');
        if (storedData) {
            console.log('📏 حجم البيانات:', storedData.length, 'حرف');
            try {
                const parsed = JSON.parse(storedData);
                console.log('✅ تم تحليل البيانات بنجاح:', parsed);
            } catch (parseError) {
                console.error('❌ خطأ في تحليل البيانات:', parseError);
                console.log('🗑️ حذف البيانات التالفة...');
                localStorage.removeItem('technoflash_settings');
            }
        }
    } catch (storageError) {
        console.error('❌ خطأ في الوصول إلى localStorage:', storageError);
    }

    // فحص قاعدة البيانات
    if (typeof db !== 'undefined' && db) {
        console.log('🗄️ قاعدة البيانات متاحة');
        try {
            const dbSettings = db.getSettings();
            console.log('📊 إعدادات قاعدة البيانات:', dbSettings);
        } catch (dbError) {
            console.error('❌ خطأ في قراءة إعدادات قاعدة البيانات:', dbError);
        }
    } else {
        console.log('❌ قاعدة البيانات غير متاحة');
    }

    // فحص الإعدادات الحالية
    console.log('⚙️ الإعدادات الحالية:', currentSettings);

    // إنشاء إعدادات افتراضية إذا لزم الأمر
    if (!currentSettings || typeof currentSettings !== 'object') {
        console.log('🔧 إنشاء إعدادات افتراضية...');
        currentSettings = getDefaultSettings();

        // حفظ الإعدادات الافتراضية
        try {
            localStorage.setItem('technoflash_settings', JSON.stringify(currentSettings));
            console.log('✅ تم حفظ الإعدادات الافتراضية');
        } catch (saveError) {
            console.error('❌ خطأ في حفظ الإعدادات الافتراضية:', saveError);
        }
    }

    console.log('🎯 انتهى التشخيص');
    return currentSettings;
}

/**
 * إعادة تعيين الإعدادات في حالة الطوارئ
 */
function emergencySettingsReset() {
    console.log('🚨 إعادة تعيين الإعدادات في حالة الطوارئ...');

    try {
        // حذف جميع البيانات المتعلقة بالإعدادات
        localStorage.removeItem('technoflash_settings');
        localStorage.removeItem('technoflash_db');
        console.log('✅ تم حذف البيانات القديمة');

        // إنشاء إعدادات افتراضية جديدة
        currentSettings = getDefaultSettings();

        // حفظ الإعدادات الجديدة
        localStorage.setItem('technoflash_settings', JSON.stringify(currentSettings));
        console.log('✅ تم حفظ الإعدادات الافتراضية الجديدة');

        // تطبيق الإعدادات
        applySettings();
        console.log('✅ تم تطبيق الإعدادات');

        // إشعار المستخدم
        if (typeof app !== 'undefined' && app && app.showNotification) {
            app.showNotification('تم إعادة تعيين الإعدادات بنجاح', 'success');
        } else {
            alert('✅ تم إعادة تعيين الإعدادات بنجاح');
        }

        return true;

    } catch (error) {
        console.error('❌ خطأ في إعادة تعيين الإعدادات:', error);

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', 'فشل في إعادة تعيين الإعدادات: ' + error.message);
        } else {
            alert('❌ فشل في إعادة تعيين الإعدادات: ' + error.message);
        }

        return false;
    }
}

/**
 * تحميل صفحة الإعدادات
 */
async function loadSettings() {
    try {
        console.log('🚀 بدء تحميل صفحة الإعدادات...');

        // تشخيص وإصلاح مشاكل الإعدادات
        debugSettingsLoad();

        const mainContent = document.getElementById('mainContent');
        if (!mainContent) {
            throw new Error('عنصر mainContent غير موجود');
        }
    
    mainContent.innerHTML = `
        <div class="page-header">
            <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="saveAllSettings()">
                    <i class="fas fa-save"></i> حفظ جميع الإعدادات
                </button>
                <button class="btn btn-warning" onclick="resetSettings()">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
            </div>
        </div>

        <!-- التبويبات -->
        <div class="tabs-container">
            <div class="tabs">
                <button class="tab-button active" onclick="switchSettingsTab('general')">
                    <i class="fas fa-cog"></i> عام
                </button>
                <button class="tab-button" onclick="switchSettingsTab('company')">
                    <i class="fas fa-building"></i> بيانات الشركة
                </button>
                <button class="tab-button" onclick="switchSettingsTab('pos')">
                    <i class="fas fa-cash-register"></i> نقطة البيع
                </button>
                <button class="tab-button" onclick="switchSettingsTab('invoice')">
                    <i class="fas fa-file-invoice"></i> الفاتورة
                </button>
                <button class="tab-button" onclick="switchSettingsTab('taxes')">
                    <i class="fas fa-percentage"></i> الضرائب
                </button>
                <button class="tab-button" onclick="switchSettingsTab('notifications')">
                    <i class="fas fa-bell"></i> التنبيهات
                </button>
                <button class="tab-button" onclick="switchSettingsTab('backup')">
                    <i class="fas fa-database"></i> النسخ الاحتياطي
                </button>
            </div>
        </div>

        <!-- محتوى التبويبات -->
        <div class="tab-content">
            <!-- الإعدادات العامة -->
            <div id="general" class="tab-pane active">
                <div class="card">
                    <div class="card-header">
                        <h3>الإعدادات العامة</h3>
                    </div>
                    <div class="card-body">
                        <form id="generalSettingsForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>اللغة</label>
                                    <select class="form-control" name="language">
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>العملة</label>
                                    <select class="form-control" name="currency">
                                        <option value="EGP">جنيه مصري (ج.م)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label>نوع التاريخ</label>
                                    <select class="form-control" name="dateType">
                                        <option value="gregorian">ميلادي</option>
                                        <option value="hijri">هجري</option>
                                        <option value="both">كلاهما</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>نوع الأرقام</label>
                                    <select class="form-control" name="numberType">
                                        <option value="arabic">عربية (١٢٣)</option>
                                        <option value="english">إنجليزية (123)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="darkMode"> الوضع الليلي
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="soundEffects"> الأصوات والتأثيرات
                                </label>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- بيانات الشركة -->
            <div id="company" class="tab-pane">
                <div class="card">
                    <div class="card-header">
                        <h3>بيانات الشركة</h3>
                    </div>
                    <div class="card-body">
                        <form id="companySettingsForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>اسم الشركة *</label>
                                    <input type="text" class="form-control" name="companyName" required>
                                </div>
                                <div class="form-group">
                                    <label>السجل التجاري</label>
                                    <input type="text" class="form-control" name="commercialRegister">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label>الرقم الضريبي</label>
                                    <input type="text" class="form-control" name="taxNumber">
                                </div>
                                <div class="form-group">
                                    <label>الهاتف</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>العنوان</label>
                                <textarea class="form-control" name="address" rows="3"></textarea>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label>البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="form-group">
                                    <label>الموقع الإلكتروني</label>
                                    <input type="url" class="form-control" name="website">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>شعار الشركة</label>
                                <input type="file" class="form-control" name="logo" accept="image/*">
                                <div id="logoPreview" class="logo-preview"></div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- إعدادات نقطة البيع -->
            <div id="pos" class="tab-pane">
                <div class="card">
                    <div class="card-header">
                        <h3>إعدادات نقطة البيع</h3>
                    </div>
                    <div class="card-body">
                        <form id="posSettingsForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>طريقة الدفع الافتراضية</label>
                                    <select class="form-control" name="defaultPaymentMethod">
                                        <option value="cash">نقداً</option>
                                        <option value="card">بطاقة</option>
                                        <option value="transfer">تحويل</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>عدد أرقام الفاتورة</label>
                                    <input type="number" class="form-control" name="invoiceNumberLength" 
                                           min="4" max="10" value="6">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="autoSave"> حفظ تلقائي للفواتير
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="autoPrint"> طباعة تلقائية للفواتير
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="barcodeScanner"> تفعيل قارئ الباركود
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="lowStockAlert"> تنبيه نفاد المخزون
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label>حد التنبيه للمخزون</label>
                                <input type="number" class="form-control" name="lowStockThreshold" 
                                       min="1" value="10">
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- إعدادات الفاتورة -->
            <div id="invoice" class="tab-pane">
                <div class="card">
                    <div class="card-header">
                        <h3>إعدادات الفاتورة</h3>
                        <button class="btn btn-sm btn-info" onclick="previewInvoice()">
                            <i class="fas fa-eye"></i> معاينة الفاتورة
                        </button>
                    </div>
                    <div class="card-body">
                        <form id="invoiceSettingsForm">
                            <!-- إعدادات الشكل والحجم -->
                            <div class="settings-section">
                                <h4><i class="fas fa-palette"></i> الشكل والحجم</h4>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>حجم الورق</label>
                                        <select class="form-control" name="paperSize" onchange="handlePaperSizeChange(); updateInvoicePreview();">
                                            <option value="A4">A4 (210 × 297 مم)</option>
                                            <option value="A5">A5 (148 × 210 مم)</option>
                                            <option value="thermal-80">حراري 80 مم</option>
                                            <option value="thermal-58">حراري 58 مم</option>
                                            <option value="custom">مخصص</option>
                                        </select>
                                    </div>
                                    <div class="form-group" id="customSizeGroup" style="display: none;">
                                        <label>الحجم المخصص (مم)</label>
                                        <div class="form-row">
                                            <input type="number" class="form-control" name="customWidth"
                                                   placeholder="العرض" min="50" max="500" onchange="updateInvoicePreview()">
                                            <input type="number" class="form-control" name="customHeight"
                                                   placeholder="الارتفاع" min="50" max="1000" onchange="updateInvoicePreview()">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>اتجاه الطباعة</label>
                                        <select class="form-control" name="orientation">
                                            <option value="portrait">عمودي</option>
                                            <option value="landscape">أفقي</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>نمط الفاتورة</label>
                                        <select class="form-control" name="invoiceTemplate" onchange="updateInvoicePreview()">
                                            <option value="classic">كلاسيكي</option>
                                            <option value="modern">عصري</option>
                                            <option value="minimal">بسيط</option>
                                            <option value="detailed">مفصل</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>لون الرأسية</label>
                                        <input type="color" class="form-control" name="headerColor" value="#2c3e50">
                                    </div>
                                    <div class="form-group">
                                        <label>لون النص</label>
                                        <input type="color" class="form-control" name="textColor" value="#333333">
                                    </div>
                                </div>
                            </div>

                            <!-- إعدادات المحتوى -->
                            <div class="settings-section">
                                <h4><i class="fas fa-list"></i> محتوى الفاتورة</h4>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showLogo" checked> عرض شعار الشركة
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showCompanyInfo" checked> عرض بيانات الشركة
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showCustomerInfo" checked> عرض بيانات العميل
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showItemCode" checked> عرض كود المنتج
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showItemDescription"> عرض وصف المنتج
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showUnitPrice" checked> عرض سعر الوحدة
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showDiscount" checked> عرض الخصم
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showTax" checked> عرض الضريبة
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showQRCode"> عرض رمز QR
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="showBarcode"> عرض الباركود
                                    </label>
                                </div>
                            </div>

                            <!-- النصوص المخصصة -->
                            <div class="settings-section">
                                <h4><i class="fas fa-font"></i> النصوص المخصصة</h4>

                                <div class="form-group">
                                    <label>عنوان الفاتورة</label>
                                    <input type="text" class="form-control" name="invoiceTitle"
                                           value="فاتورة مبيعات" placeholder="فاتورة مبيعات">
                                </div>

                                <div class="form-group">
                                    <label>رسالة الشكر</label>
                                    <textarea class="form-control" name="thankYouMessage" rows="2"
                                              placeholder="شكراً لتعاملكم معنا"></textarea>
                                </div>

                                <div class="form-group">
                                    <label>شروط وأحكام</label>
                                    <textarea class="form-control" name="termsConditions" rows="3"
                                              placeholder="الشروط والأحكام..."></textarea>
                                </div>

                                <div class="form-group">
                                    <label>ملاحظات إضافية</label>
                                    <textarea class="form-control" name="footerNotes" rows="2"
                                              placeholder="ملاحظات في أسفل الفاتورة..."></textarea>
                                </div>
                            </div>

                            <!-- إعدادات الطباعة -->
                            <div class="settings-section">
                                <h4><i class="fas fa-print"></i> إعدادات الطباعة</h4>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>الهوامش (مم)</label>
                                        <div class="margin-inputs">
                                            <input type="number" class="form-control" name="marginTop"
                                                   placeholder="أعلى" min="0" max="50" value="10">
                                            <input type="number" class="form-control" name="marginRight"
                                                   placeholder="يمين" min="0" max="50" value="10">
                                            <input type="number" class="form-control" name="marginBottom"
                                                   placeholder="أسفل" min="0" max="50" value="10">
                                            <input type="number" class="form-control" name="marginLeft"
                                                   placeholder="يسار" min="0" max="50" value="10">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>حجم الخط الأساسي</label>
                                        <select class="form-control" name="fontSize">
                                            <option value="10">10px</option>
                                            <option value="12" selected>12px</option>
                                            <option value="14">14px</option>
                                            <option value="16">16px</option>
                                            <option value="18">18px</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>نوع الخط</label>
                                        <select class="form-control" name="fontFamily">
                                            <option value="Cairo">Cairo</option>
                                            <option value="Amiri">Amiri</option>
                                            <option value="Tajawal">Tajawal</option>
                                            <option value="Arial">Arial</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>عدد النسخ</label>
                                    <input type="number" class="form-control" name="printCopies"
                                           min="1" max="5" value="1">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- إعدادات الضرائب -->
            <div id="taxes" class="tab-pane">
                <div class="card">
                    <div class="card-header">
                        <h3>إعدادات الضرائب</h3>
                        <button class="btn btn-sm btn-primary" onclick="addTaxRate()">
                            <i class="fas fa-plus"></i> إضافة ضريبة
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="enableTax" onchange="toggleTaxSettings()"> 
                                تفعيل الضرائب
                            </label>
                        </div>
                        
                        <div id="taxSettings" style="display: none;">
                            <div class="form-group">
                                <label>نوع الضريبة الافتراضي</label>
                                <select class="form-control" name="defaultTaxType">
                                    <option value="inclusive">شامل الضريبة</option>
                                    <option value="exclusive">غير شامل الضريبة</option>
                                </select>
                            </div>
                            
                            <div class="tax-rates-section">
                                <h4>معدلات الضرائب</h4>
                                <div id="taxRatesList">
                                    <!-- سيتم تحميل معدلات الضرائب هنا -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات التنبيهات -->
            <div id="notifications" class="tab-pane">
                <div class="card">
                    <div class="card-header">
                        <h3>إعدادات التنبيهات</h3>
                    </div>
                    <div class="card-body">
                        <form id="notificationSettingsForm">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="enableNotifications"> تفعيل التنبيهات
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="stockAlerts"> تنبيهات المخزون
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="paymentReminders"> تذكير المدفوعات
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="dailyReports"> التقارير اليومية
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label>وقت التقرير اليومي</label>
                                <input type="time" class="form-control" name="dailyReportTime" value="18:00">
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- إعدادات النسخ الاحتياطي -->
            <div id="backup" class="tab-pane">
                <div class="card">
                    <div class="card-header">
                        <h3>إعدادات النسخ الاحتياطي</h3>
                    </div>
                    <div class="card-body">
                        <form id="backupSettingsForm">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="autoBackup" onchange="toggleAutoBackup()"> 
                                    النسخ الاحتياطي التلقائي
                                </label>
                            </div>
                            
                            <div id="autoBackupSettings" style="display: none;">
                                <div class="form-group">
                                    <label>تكرار النسخ الاحتياطي</label>
                                    <select class="form-control" name="backupFrequency">
                                        <option value="daily">يومياً</option>
                                        <option value="weekly">أسبوعياً</option>
                                        <option value="monthly">شهرياً</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label>وقت النسخ الاحتياطي</label>
                                    <input type="time" class="form-control" name="backupTime" value="02:00">
                                </div>
                                
                                <div class="form-group">
                                    <label>عدد النسخ المحفوظة</label>
                                    <input type="number" class="form-control" name="maxBackups" 
                                           min="1" max="30" value="7">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;

        // تهيئة الصفحة بعد تأخير صغير لضمان تحميل DOM
        setTimeout(async () => {
            await initializeSettingsPage();
        }, 100);

    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة الإعدادات:', error);

        const mainContent = document.getElementById('mainContent');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="error-page" style="text-align: center; padding: 2rem; color: var(--error-color, #dc3545);">
                    <h2><i class="fas fa-exclamation-triangle"></i> خطأ في تحميل الإعدادات</h2>
                    <p>حدث خطأ أثناء تحميل صفحة الإعدادات: ${error.message || 'خطأ غير معروف'}</p>
                    <div style="margin-top: 1rem;">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh"></i> إعادة تحميل الصفحة
                        </button>
                        <button class="btn btn-warning" onclick="debugSettingsLoad(); location.reload();" style="margin-right: 10px;">
                            <i class="fas fa-tools"></i> إصلاح وإعادة تحميل
                        </button>
                    </div>
                </div>
            `;
        }

        // عرض تنبيه للمستخدم
        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', `حدث خطأ في تحميل صفحة الإعدادات: ${error.message || 'خطأ غير معروف'}`);
        }
    }
}

/**
 * إعادة تعيين العملة للجنيه المصري فوراً (العملة الوحيدة المدعومة)
 */
function forceResetToEgyptianPound() {
    console.log('إعادة تعيين فورية للجنيه المصري - العملة الوحيدة المدعومة...');

    // حذف جميع البيانات المحفوظة
    localStorage.clear();
    console.log('تم حذف جميع البيانات المحفوظة');

    // تطبيق الجنيه المصري فوراً
    document.documentElement.setAttribute('data-currency', 'EGP');

    // إعادة تحميل الصفحة
    alert('تم تطبيق الجنيه المصري كعملة وحيدة للنظام');
    window.location.reload();
}

/**
 * تهيئة صفحة الإعدادات
 */
async function initializeSettingsPage() {
    try {
        console.log('🔄 بدء تهيئة صفحة الإعدادات...');

        // التحقق من وجود العناصر الأساسية
        const mainContent = document.getElementById('mainContent');
        if (!mainContent) {
            console.error('❌ عنصر mainContent غير موجود');
            throw new Error('عنصر mainContent غير موجود');
        }

        // تحميل الإعدادات الحالية
        console.log('📥 تحميل الإعدادات الحالية...');
        try {
            loadCurrentSettings();
            console.log('✅ تم تحميل الإعدادات بنجاح');
        } catch (settingsError) {
            console.error('❌ خطأ في تحميل الإعدادات:', settingsError);
            // المتابعة بالإعدادات الافتراضية
            currentSettings = getDefaultSettings();
        }

        // انتظار قصير للتأكد من تحميل DOM
        console.log('⏳ انتظار تحميل DOM...');
        await new Promise(resolve => setTimeout(resolve, 200));

        // التحقق من وجود التبويبات
        const tabsContainer = document.querySelector('.tabs-container');
        if (!tabsContainer) {
            console.error('❌ حاوية التبويبات غير موجودة');
            throw new Error('حاوية التبويبات غير موجودة');
        }

        // ملء النماذج بالبيانات
        console.log('📝 ملء النماذج بالبيانات...');
        try {
            populateSettingsForms();
            console.log('✅ تم ملء النماذج بنجاح');
        } catch (formsError) {
            console.error('❌ خطأ في ملء النماذج:', formsError);
            // عرض رسالة خطأ للمستخدم
            if (typeof app !== 'undefined' && app && app.showAlert) {
                app.showAlert('تحذير', 'حدث خطأ في ملء بعض النماذج، قد تحتاج لإعادة تحميل الصفحة');
            }
        }

        // إعداد مستمعي الأحداث
        console.log('🎧 إعداد مستمعي الأحداث...');
        try {
            setupSettingsEventListeners();
            console.log('✅ تم إعداد مستمعي الأحداث بنجاح');
        } catch (eventsError) {
            console.error('❌ خطأ في إعداد مستمعي الأحداث:', eventsError);
        }

        // تطبيق الإعدادات
        console.log('⚙️ تطبيق الإعدادات...');
        try {
            applySettings();
            console.log('✅ تم تطبيق الإعدادات بنجاح');
        } catch (applyError) {
            console.error('❌ خطأ في تطبيق الإعدادات:', applyError);
        }

        console.log('🎉 تم تهيئة صفحة الإعدادات بنجاح');

    } catch (error) {
        console.error('💥 خطأ فادح في تهيئة صفحة الإعدادات:', error);

        // عرض رسالة خطأ مفصلة للمستخدم
        const errorMessage = `حدث خطأ في تحميل صفحة الإعدادات: ${error.message || 'خطأ غير معروف'}`;

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', errorMessage);
        } else {
            alert('❌ ' + errorMessage);
        }

        // محاولة عرض صفحة خطأ بسيطة
        try {
            const mainContent = document.getElementById('mainContent');
            if (mainContent) {
                mainContent.innerHTML = `
                    <div class="error-page" style="text-align: center; padding: 2rem; color: var(--error-color, #dc3545);">
                        <h2><i class="fas fa-exclamation-triangle"></i> خطأ في تحميل الإعدادات</h2>
                        <p>${errorMessage}</p>
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh"></i> إعادة تحميل الصفحة
                        </button>
                    </div>
                `;
            }
        } catch (displayError) {
            console.error('خطأ في عرض صفحة الخطأ:', displayError);
        }
    }
}

/**
 * إعادة تعيين الإعدادات للجنيه المصري
 */
function resetToEgyptianPound() {
    console.log('إعادة تعيين الإعدادات للجنيه المصري');

    // حذف الإعدادات القديمة
    try {
        localStorage.removeItem('technoflash_settings');
        localStorage.removeItem('technoflash_db');
        console.log('تم حذف الإعدادات القديمة');
    } catch (e) {
        console.error('خطأ في حذف الإعدادات القديمة:', e);
    }

    // تعيين الإعدادات الجديدة
    currentSettings = getDefaultSettings();

    // حفظ الإعدادات الجديدة
    try {
        localStorage.setItem('technoflash_settings', JSON.stringify(currentSettings));
        console.log('تم حفظ الإعدادات الجديدة بالجنيه المصري');
    } catch (e) {
        console.error('خطأ في حفظ الإعدادات الجديدة:', e);
    }

    // تطبيق الإعدادات فوراً
    applySettings();

    return currentSettings;
}

/**
 * تحميل الإعدادات الحالية
 */
function loadCurrentSettings() {
    try {
        console.log('بدء تحميل الإعدادات الحالية...');

        // إعادة تعيين الإعدادات الحالية
        currentSettings = null;

        // محاولة تحميل الإعدادات من مصادر متعددة
        if (typeof db !== 'undefined' && db) {
            console.log('محاولة تحميل الإعدادات من قاعدة البيانات...');

            // محاولة تحميل الإعدادات من localStorage المنفصل أولاً
            try {
                currentSettings = db.getStoredSettings();
                if (currentSettings) {
                    console.log('✅ تم تحميل الإعدادات من localStorage المنفصل');
                }
            } catch (e) {
                console.error('خطأ في تحميل الإعدادات من localStorage المنفصل:', e);
            }

            // إذا لم توجد، محاولة تحميلها من البيانات الرئيسية
            if (!currentSettings) {
                try {
                    currentSettings = db.getSettings();
                    if (currentSettings) {
                        console.log('✅ تم تحميل الإعدادات من قاعدة البيانات الرئيسية');
                    }
                } catch (e) {
                    console.error('خطأ في تحميل الإعدادات من قاعدة البيانات الرئيسية:', e);
                }
            }
        } else {
            console.log('قاعدة البيانات غير متاحة، محاولة التحميل المباشر من localStorage...');
        }

        // تحميل مباشر من localStorage كبديل
        if (!currentSettings) {
            try {
                console.log('محاولة التحميل المباشر من localStorage...');
                const storedSettings = localStorage.getItem('technoflash_settings');
                if (storedSettings && storedSettings.trim() !== '') {
                    currentSettings = JSON.parse(storedSettings);
                    console.log('✅ تم تحميل الإعدادات من localStorage مباشرة');
                } else {
                    console.log('لا توجد إعدادات محفوظة في localStorage');
                }
            } catch (e) {
                console.error('فشل في تحميل الإعدادات من localStorage:', e);
                currentSettings = null;
            }
        }

        // التحقق من صحة الإعدادات المحملة
        if (currentSettings && typeof currentSettings === 'object') {
            // التأكد من وجود الأقسام الأساسية
            if (!currentSettings.general) {
                console.log('إضافة الإعدادات العامة المفقودة...');
                currentSettings.general = getDefaultSettings().general;
            }
            if (!currentSettings.company) {
                console.log('إضافة بيانات الشركة المفقودة...');
                currentSettings.company = getDefaultSettings().company;
            }
            if (!currentSettings.pos) {
                console.log('إضافة إعدادات نقطة البيع المفقودة...');
                currentSettings.pos = getDefaultSettings().pos;
            }

            // التأكد من أن العملة هي الجنيه المصري فقط
            if (currentSettings.general && currentSettings.general.currency !== 'EGP') {
                console.log('تصحيح العملة إلى الجنيه المصري...');
                currentSettings.general.currency = 'EGP';
            }

            console.log('✅ تم التحقق من صحة الإعدادات المحملة');
        }

        // إذا لم توجد أي إعدادات صحيحة، استخدام الافتراضية
        if (!currentSettings || typeof currentSettings !== 'object') {
            console.log('استخدام الإعدادات الافتراضية...');
            currentSettings = getDefaultSettings();
        }

        console.log('✅ تم تحميل الإعدادات بنجاح:', currentSettings);

    } catch (error) {
        console.error('❌ خطأ في تحميل الإعدادات:', error);
        currentSettings = getDefaultSettings();

        // عرض رسالة تحذير للمستخدم
        const warningMessage = 'تم تحميل الإعدادات الافتراضية بسبب خطأ في قراءة الإعدادات المحفوظة';
        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('تحذير', warningMessage);
        } else {
            console.warn(warningMessage);
            // عرض تنبيه بسيط إذا لم تكن دالة showAlert متاحة
            setTimeout(() => {
                alert('⚠️ ' + warningMessage);
            }, 1000);
        }

        // تطبيق الإعدادات الافتراضية
        try {
            applySettings();
        } catch (applyError) {
            console.error('خطأ في تطبيق الإعدادات الافتراضية:', applyError);
        }
    }
}

/**
 * الحصول على الإعدادات الافتراضية
 */
function getDefaultSettings() {
    return {
        general: {
            language: 'ar',
            currency: 'EGP',
            dateType: 'gregorian',
            numberType: 'arabic',
            darkMode: false,
            soundEffects: true
        },
        company: {
            companyName: 'تكنوفلاش',
            commercialRegister: '',
            taxNumber: '',
            phone: '',
            address: '',
            email: '',
            website: '',
            logo: ''
        },
        pos: {
            defaultPaymentMethod: 'cash',
            invoiceNumberLength: 6,
            autoSave: true,
            autoPrint: false,
            barcodeScanner: true,
            lowStockAlert: true,
            lowStockThreshold: 10
        },
        taxes: {
            enableTax: false,
            defaultTaxType: 'inclusive',
            taxRates: [
                { name: 'ضريبة القيمة المضافة', rate: 15, isDefault: true }
            ]
        },
        notifications: {
            enableNotifications: true,
            stockAlerts: true,
            paymentReminders: true,
            dailyReports: false,
            dailyReportTime: '18:00'
        },
        backup: {
            autoBackup: false,
            backupFrequency: 'daily',
            backupTime: '02:00',
            maxBackups: 7
        },
        invoice: {
            paperSize: 'A4',
            customWidth: 210,
            customHeight: 297,
            orientation: 'portrait',
            invoiceTemplate: 'classic',
            headerColor: '#2c3e50',
            textColor: '#333333',
            showLogo: true,
            showCompanyInfo: true,
            showCustomerInfo: true,
            showItemCode: false,
            showItemDescription: true,
            showUnitPrice: true,
            showTotal: true,
            showTax: false,
            showQRCode: false,
            showBarcode: false,
            invoiceTitle: 'فاتورة مبيعات',
            thankYouMessage: 'شكراً لتعاملكم معنا',
            termsConditions: '',
            footerNotes: '',
            marginTop: 10,
            marginRight: 10,
            marginBottom: 10,
            marginLeft: 10,
            fontSize: 12,
            lineHeight: 1.5,
            printCopies: 1
        }
    };
}

/**
 * ملء النماذج بالبيانات
 */
function populateSettingsForms() {
    try {
        console.log('بدء ملء النماذج بالبيانات');
        console.log('الإعدادات الحالية:', currentSettings);

        // التحقق من وجود الإعدادات
        if (!currentSettings) {
            console.error('الإعدادات غير موجودة');
            return;
        }

        // الإعدادات العامة
        const generalForm = document.getElementById('generalSettingsForm');
        console.log('نموذج الإعدادات العامة:', generalForm);

        if (generalForm && currentSettings.general) {
            console.log('ملء نموذج الإعدادات العامة...');
            Object.keys(currentSettings.general).forEach(key => {
                const element = generalForm.querySelector(`[name="${key}"]`);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = currentSettings.general[key];
                    } else {
                        element.value = currentSettings.general[key];
                    }
                    console.log(`تم تعيين ${key} = ${currentSettings.general[key]}`);
                } else {
                    console.warn(`عنصر ${key} غير موجود في النموذج`);
                }
            });
            console.log('تم ملء نموذج الإعدادات العامة بنجاح');
        } else {
            console.warn('لم يتم العثور على نموذج الإعدادات العامة أو البيانات');
            console.log('generalForm:', generalForm);
            console.log('currentSettings.general:', currentSettings.general);
        }

        // بيانات الشركة
        const companyForm = document.getElementById('companySettingsForm');
        if (companyForm && currentSettings.company) {
            Object.keys(currentSettings.company).forEach(key => {
                const element = companyForm.querySelector(`[name="${key}"]`);
                if (element && key !== 'logo') {
                    element.value = currentSettings.company[key];
                }
            });
        } else {
            console.warn('لم يتم العثور على نموذج بيانات الشركة');
        }

        // إعدادات نقطة البيع
        const posForm = document.getElementById('posSettingsForm');
        if (posForm && currentSettings.pos) {
            Object.keys(currentSettings.pos).forEach(key => {
                const element = posForm.querySelector(`[name="${key}"]`);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = currentSettings.pos[key];
                    } else {
                        element.value = currentSettings.pos[key];
                    }
                }
            });
        } else {
            console.warn('لم يتم العثور على نموذج إعدادات نقطة البيع');
        }

        // إعدادات الضرائب
        const enableTaxElement = document.querySelector('[name="enableTax"]');
        const defaultTaxTypeElement = document.querySelector('[name="defaultTaxType"]');
        if (enableTaxElement && currentSettings.taxes) {
            enableTaxElement.checked = currentSettings.taxes.enableTax;
        }
        if (defaultTaxTypeElement && currentSettings.taxes) {
            defaultTaxTypeElement.value = currentSettings.taxes.defaultTaxType;
        }
        if (typeof toggleTaxSettings === 'function') {
            toggleTaxSettings();
        }
        if (typeof loadTaxRates === 'function') {
            loadTaxRates();
        }

        // إعدادات التنبيهات
        const notificationForm = document.getElementById('notificationSettingsForm');
        if (notificationForm && currentSettings.notifications) {
            Object.keys(currentSettings.notifications).forEach(key => {
                const element = notificationForm.querySelector(`[name="${key}"]`);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = currentSettings.notifications[key];
                    } else {
                        element.value = currentSettings.notifications[key];
                    }
                }
            });
        } else {
            console.warn('لم يتم العثور على نموذج إعدادات التنبيهات');
        }

        // إعدادات النسخ الاحتياطي
        const backupForm = document.getElementById('backupSettingsForm');
        if (backupForm && currentSettings.backup) {
            Object.keys(currentSettings.backup).forEach(key => {
                const element = backupForm.querySelector(`[name="${key}"]`);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = currentSettings.backup[key];
                    } else {
                        element.value = currentSettings.backup[key];
                    }
                }
            });
        } else {
            console.warn('لم يتم العثور على نموذج إعدادات النسخ الاحتياطي');
        }

        if (typeof toggleAutoBackup === 'function') {
            toggleAutoBackup();
        }

        // إعدادات الفاتورة
        const invoiceForm = document.getElementById('invoiceSettingsForm');
        if (invoiceForm && currentSettings.invoice) {
            Object.keys(currentSettings.invoice).forEach(key => {
                const element = invoiceForm.querySelector(`[name="${key}"]`);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = currentSettings.invoice[key];
                    } else if (element.type === 'color') {
                        element.value = currentSettings.invoice[key];
                    } else {
                        element.value = currentSettings.invoice[key];
                    }
                }
            });

            // التعامل مع الحجم المخصص
            if (currentSettings.invoice.paperSize === 'custom') {
                const customSizeGroup = document.getElementById('customSizeGroup');
                if (customSizeGroup) {
                    customSizeGroup.style.display = 'block';
                }
            }

            console.log('تم ملء نموذج إعدادات الفاتورة');
        } else {
            console.warn('لم يتم العثور على نموذج إعدادات الفاتورة');
        }

        // تطبيق الإعدادات بعد ملء النماذج
        applySettings();

    } catch (error) {
        console.error('خطأ في ملء النماذج:', error);
        if (app && app.showAlert) {
            app.showAlert('خطأ', 'حدث خطأ في تحميل الإعدادات');
        } else {
            console.error('حدث خطأ في تحميل الإعدادات');
        }
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupSettingsEventListeners() {
    try {
        // مستمع تغيير الشعار
        const logoInput = document.querySelector('[name="logo"]');
        if (logoInput) {
            logoInput.addEventListener('change', handleLogoChange);
        }

        // مستمعي أحداث أخرى يمكن إضافتها هنا
        console.log('تم إعداد مستمعي الأحداث للإعدادات');
    } catch (error) {
        console.error('خطأ في إعداد مستمعي الأحداث:', error);
    }
}

/**
 * تبديل تبويبات الإعدادات
 */
function switchSettingsTab(tabId) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });

    // إزالة الحالة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });

    // إظهار التبويب المحدد
    document.getElementById(tabId).classList.add('active');

    // تفعيل الزر المناسب
    document.querySelector(`[onclick="switchSettingsTab('${tabId}')"]`).classList.add('active');
}

/**
 * تفعيل/إلغاء إعدادات الضرائب
 */
function toggleTaxSettings() {
    const enableTax = document.querySelector('[name="enableTax"]').checked;
    const taxSettings = document.getElementById('taxSettings');

    if (enableTax) {
        taxSettings.style.display = 'block';
    } else {
        taxSettings.style.display = 'none';
    }
}

/**
 * تفعيل/إلغاء النسخ الاحتياطي التلقائي
 */
function toggleAutoBackup() {
    const autoBackup = document.querySelector('[name="autoBackup"]').checked;
    const autoBackupSettings = document.getElementById('autoBackupSettings');

    if (autoBackup) {
        autoBackupSettings.style.display = 'block';
    } else {
        autoBackupSettings.style.display = 'none';
    }
}

/**
 * تحميل معدلات الضرائب
 */
function loadTaxRates() {
    const taxRatesList = document.getElementById('taxRatesList');

    taxRatesList.innerHTML = currentSettings.taxes.taxRates.map((rate, index) => `
        <div class="tax-rate-item">
            <div class="form-row">
                <div class="form-group">
                    <input type="text" class="form-control" value="${rate.name}"
                           onchange="updateTaxRate(${index}, 'name', this.value)">
                </div>
                <div class="form-group">
                    <div class="input-group">
                        <input type="number" class="form-control" value="${rate.rate}"
                               step="0.01" min="0" max="100"
                               onchange="updateTaxRate(${index}, 'rate', this.value)">
                        <div class="input-group-append">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" ${rate.isDefault ? 'checked' : ''}
                               onchange="updateTaxRate(${index}, 'isDefault', this.checked)">
                        افتراضي
                    </label>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-sm btn-danger"
                            onclick="removeTaxRate(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * إضافة معدل ضريبة جديد
 */
function addTaxRate() {
    const newRate = {
        name: 'ضريبة جديدة',
        rate: 0,
        isDefault: false
    };

    currentSettings.taxes.taxRates.push(newRate);
    loadTaxRates();
}

/**
 * تحديث معدل ضريبة
 */
function updateTaxRate(index, field, value) {
    if (field === 'rate') {
        value = parseFloat(value) || 0;
    } else if (field === 'isDefault') {
        // إلغاء تحديد الافتراضي من الآخرين
        if (value) {
            currentSettings.taxes.taxRates.forEach((rate, i) => {
                if (i !== index) rate.isDefault = false;
            });
        }
    }

    currentSettings.taxes.taxRates[index][field] = value;

    if (field === 'isDefault') {
        loadTaxRates();
    }
}

/**
 * حذف معدل ضريبة
 */
function removeTaxRate(index) {
    if (currentSettings.taxes.taxRates.length <= 1) {
        if (app && app.showAlert) {
            app.showAlert('خطأ', 'يجب أن يكون هناك معدل ضريبة واحد على الأقل');
        } else {
            alert('خطأ: يجب أن يكون هناك معدل ضريبة واحد على الأقل');
        }
        return;
    }

    if (app && app.showConfirm) {
        app.showConfirm(
            'حذف معدل الضريبة',
            'هل أنت متأكد من حذف معدل الضريبة هذا؟',
            () => {
                currentSettings.taxes.taxRates.splice(index, 1);
                loadTaxRates();
            }
        );
    } else {
        if (confirm('هل أنت متأكد من حذف معدل الضريبة هذا؟')) {
            currentSettings.taxes.taxRates.splice(index, 1);
            loadTaxRates();
        }
    }
}

/**
 * معالجة تغيير الشعار
 */
function handleLogoChange(e) {
    const file = e.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        app.showAlert('خطأ', 'يرجى اختيار ملف صورة صحيح');
        return;
    }

    // التحقق من حجم الملف (أقل من 2MB)
    if (file.size > 2 * 1024 * 1024) {
        app.showAlert('خطأ', 'حجم الصورة يجب أن يكون أقل من 2 ميجابايت');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const logoPreview = document.getElementById('logoPreview');
        logoPreview.innerHTML = `
            <img src="${e.target.result}" alt="شعار الشركة" class="logo-image">
            <button type="button" class="btn btn-sm btn-danger" onclick="removeLogo()">
                <i class="fas fa-trash"></i> حذف الشعار
            </button>
        `;

        currentSettings.company.logo = e.target.result;
    };

    reader.readAsDataURL(file);
}

/**
 * حذف الشعار
 */
function removeLogo() {
    document.getElementById('logoPreview').innerHTML = '';
    document.querySelector('[name="logo"]').value = '';
    currentSettings.company.logo = '';
}

/**
 * حفظ جميع الإعدادات
 */
async function saveAllSettings() {
    console.log('🔄 بدء عملية حفظ الإعدادات...');

    // إضافة مؤشر بصري للمستخدم
    const saveButton = document.querySelector('button[onclick="saveAllSettings()"]');
    if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    }

    // عرض مؤشر التحميل إذا كان متاحاً
    if (typeof app !== 'undefined' && app && app.showLoading) {
        app.showLoading();
    }

    try {
        // التأكد من تهيئة currentSettings
        if (!currentSettings || typeof currentSettings !== 'object') {
            console.log('تهيئة currentSettings...');
            currentSettings = getDefaultSettings();
        }

        console.log('الإعدادات الحالية قبل الجمع:', currentSettings);

        // جمع البيانات من جميع النماذج
        collectSettingsData();

        console.log('الإعدادات بعد الجمع:', currentSettings);

        // التحقق من صحة البيانات
        const validation = validateSettings();
        if (!validation.isValid) {
            console.error('فشل التحقق من صحة البيانات:', validation.message);
            if (typeof app !== 'undefined' && app && app.showAlert) {
                app.showAlert('خطأ في البيانات', validation.message);
            } else {
                alert('خطأ في البيانات: ' + validation.message);
            }
            if (typeof app !== 'undefined' && app && app.hideLoading) {
                app.hideLoading();
            }
            return;
        }

        console.log('تم التحقق من صحة البيانات بنجاح');

        // حفظ الإعدادات في localStorage المنفصل
        let saveResult = false;
        let saveMethod = '';

        // محاولة الحفظ باستخدام قاعدة البيانات أولاً
        if (typeof db !== 'undefined' && db && db.saveSettings) {
            try {
                console.log('محاولة الحفظ باستخدام قاعدة البيانات...');
                saveResult = db.saveSettings(currentSettings);
                saveMethod = 'database';
                console.log('✅ تم حفظ الإعدادات باستخدام قاعدة البيانات');
            } catch (e) {
                console.error('❌ فشل في حفظ الإعدادات باستخدام قاعدة البيانات:', e);
                saveResult = false;
            }
        } else {
            console.log('⚠️ قاعدة البيانات غير متاحة، سيتم استخدام localStorage مباشرة');
        }

        // إذا فشل الحفظ باستخدام قاعدة البيانات، جرب localStorage مباشرة
        if (!saveResult) {
            try {
                console.log('محاولة الحفظ في localStorage مباشرة...');
                const settingsString = JSON.stringify(currentSettings);
                localStorage.setItem('technoflash_settings', settingsString);
                saveResult = true;
                saveMethod = 'localStorage';
                console.log('✅ تم حفظ الإعدادات في localStorage مباشرة');
                console.log('حجم البيانات المحفوظة:', settingsString.length, 'حرف');
            } catch (e) {
                console.error('❌ فشل في حفظ الإعدادات في localStorage:', e);
                saveResult = false;
            }
        }

        if (!saveResult) {
            throw new Error('فشل في حفظ الإعدادات في جميع الطرق المتاحة');
        }

        console.log(`تم حفظ الإعدادات بنجاح باستخدام: ${saveMethod}`);

        // تطبيق الإعدادات
        applySettings();

        console.log('تم تطبيق الإعدادات بنجاح');

        // إرسال حدث تحديث الإعدادات
        try {
            window.dispatchEvent(new CustomEvent('settingsUpdated', {
                detail: currentSettings
            }));
            console.log('تم إرسال حدث تحديث الإعدادات');
        } catch (e) {
            console.error('خطأ في إرسال حدث تحديث الإعدادات:', e);
        }

        // عرض رسالة النجاح
        if (typeof app !== 'undefined' && app && app.showNotification) {
            app.showNotification('تم حفظ الإعدادات بنجاح', 'success');
        } else {
            alert('✅ تم حفظ الإعدادات بنجاح');
        }

        console.log('✅ تمت عملية حفظ الإعدادات بنجاح');

    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);

        let errorMessage = 'حدث خطأ في حفظ الإعدادات';
        if (error.message) {
            errorMessage += ': ' + error.message;
        }

        if (typeof app !== 'undefined' && app && app.showAlert) {
            app.showAlert('خطأ', errorMessage);
        } else {
            alert('❌ خطأ: ' + errorMessage);
        }
    } finally {
        if (typeof app !== 'undefined' && app && app.hideLoading) {
            app.hideLoading();
        }

        // استعادة زر الحفظ
        const saveButton = document.querySelector('button[onclick="saveAllSettings()"]');
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.innerHTML = '<i class="fas fa-save"></i> حفظ جميع الإعدادات';
        }
    }
}

/**
 * جمع بيانات الإعدادات من النماذج
 */
function collectSettingsData() {
    try {
        console.log('بدء جمع بيانات الإعدادات من النماذج');

        // التحقق من وجود currentSettings
        if (!currentSettings) {
            currentSettings = getDefaultSettings();
        }

        // الإعدادات العامة
        const generalForm = document.getElementById('generalSettingsForm');
        if (generalForm) {
            console.log('جمع بيانات الإعدادات العامة...');
            const generalData = new FormData(generalForm);
            currentSettings.general = {
                language: generalData.get('language') || 'ar',
                currency: generalData.get('currency') || 'EGP',
                dateType: generalData.get('dateType') || 'gregorian',
                numberType: generalData.get('numberType') || 'arabic',
                darkMode: generalData.has('darkMode'),
                soundEffects: generalData.has('soundEffects')
            };
            console.log('تم جمع الإعدادات العامة:', currentSettings.general);
        } else {
            console.warn('نموذج الإعدادات العامة غير موجود - استخدام القيم الافتراضية');
            if (!currentSettings.general) {
                currentSettings.general = getDefaultSettings().general;
            }
        }

        // بيانات الشركة
        const companyForm = document.getElementById('companySettingsForm');
        if (companyForm) {
            console.log('جمع بيانات الشركة...');
            const companyData = new FormData(companyForm);
            currentSettings.company = {
                companyName: companyData.get('companyName') || 'تكنوفلاش',
                commercialRegister: companyData.get('commercialRegister') || '',
                taxNumber: companyData.get('taxNumber') || '',
                phone: companyData.get('phone') || '',
                address: companyData.get('address') || '',
                email: companyData.get('email') || '',
                website: companyData.get('website') || '',
                logo: currentSettings.company ? currentSettings.company.logo || '' : ''
            };
            console.log('تم جمع بيانات الشركة:', currentSettings.company);
        } else {
            console.warn('نموذج بيانات الشركة غير موجود - استخدام القيم الافتراضية');
            if (!currentSettings.company) {
                currentSettings.company = getDefaultSettings().company;
            }
        }

        // إعدادات نقطة البيع
        const posForm = document.getElementById('posSettingsForm');
        if (posForm) {
            console.log('جمع إعدادات نقطة البيع...');
        } else {
            console.warn('نموذج إعدادات نقطة البيع غير موجود - استخدام القيم الافتراضية');
            if (!currentSettings.pos) {
                currentSettings.pos = getDefaultSettings().pos;
            }
            return; // الخروج مبكراً إذا لم توجد النماذج
        }

        const posData = new FormData(posForm);
        currentSettings.pos = {
            defaultPaymentMethod: posData.get('defaultPaymentMethod') || 'cash',
            invoiceNumberLength: parseInt(posData.get('invoiceNumberLength')) || 6,
            autoSave: posData.has('autoSave'),
            autoPrint: posData.has('autoPrint'),
            barcodeScanner: posData.has('barcodeScanner'),
            lowStockAlert: posData.has('lowStockAlert'),
            lowStockThreshold: parseInt(posData.get('lowStockThreshold')) || 10
        };
        console.log('تم جمع إعدادات نقطة البيع:', currentSettings.pos);

        // إعدادات الضرائب
        const enableTaxElement = document.querySelector('[name="enableTax"]');
        const defaultTaxTypeElement = document.querySelector('[name="defaultTaxType"]');

        currentSettings.taxes = {
            enableTax: enableTaxElement ? enableTaxElement.checked : false,
            defaultTaxType: defaultTaxTypeElement ? defaultTaxTypeElement.value : 'inclusive',
            taxRates: currentSettings.taxes ? currentSettings.taxes.taxRates : [
                { name: 'ضريبة القيمة المضافة', rate: 15, isDefault: true }
            ]
        };
        console.log('تم جمع إعدادات الضرائب:', currentSettings.taxes);

        // إعدادات التنبيهات
        const notificationForm = document.getElementById('notificationSettingsForm');
        if (!notificationForm) {
            throw new Error('نموذج إعدادات التنبيهات غير موجود');
        }

        const notificationData = new FormData(notificationForm);
        currentSettings.notifications = {
            enableNotifications: notificationData.has('enableNotifications'),
            stockAlerts: notificationData.has('stockAlerts'),
            paymentReminders: notificationData.has('paymentReminders'),
            dailyReports: notificationData.has('dailyReports'),
            dailyReportTime: notificationData.get('dailyReportTime') || '18:00'
        };
        console.log('تم جمع إعدادات التنبيهات:', currentSettings.notifications);

        // إعدادات النسخ الاحتياطي
        const backupForm = document.getElementById('backupSettingsForm');
        if (!backupForm) {
            throw new Error('نموذج إعدادات النسخ الاحتياطي غير موجود');
        }

        const backupData = new FormData(backupForm);
        currentSettings.backup = {
            autoBackup: backupData.has('autoBackup'),
            backupFrequency: backupData.get('backupFrequency') || 'daily',
            backupTime: backupData.get('backupTime') || '02:00',
            maxBackups: parseInt(backupData.get('maxBackups')) || 7
        };
        console.log('تم جمع إعدادات النسخ الاحتياطي:', currentSettings.backup);

        // إعدادات الفاتورة
        const invoiceForm = document.getElementById('invoiceSettingsForm');
        if (invoiceForm) {
            const invoiceSettings = collectInvoiceSettings();
            currentSettings.invoice = invoiceSettings;
            console.log('تم جمع إعدادات الفاتورة:', currentSettings.invoice);
        } else {
            console.warn('نموذج إعدادات الفاتورة غير موجود');
            // إعدادات افتراضية للفاتورة
            currentSettings.invoice = {
                paperSize: 'A4',
                orientation: 'portrait',
                invoiceTemplate: 'classic',
                headerColor: '#2c3e50',
                textColor: '#333333',
                showLogo: true,
                showCompanyInfo: true,
                showCustomerInfo: true,
                invoiceTitle: 'فاتورة مبيعات',
                marginTop: 10,
                marginRight: 10,
                marginBottom: 10,
                marginLeft: 10,
                fontSize: 12,
                printCopies: 1
            };
        }

        console.log('تم جمع جميع الإعدادات بنجاح:', currentSettings);

    } catch (error) {
        console.error('خطأ في جمع بيانات الإعدادات:', error);
        throw error;
    }
}

/**
 * التحقق من صحة الإعدادات
 */
function validateSettings() {
    try {
        console.log('بدء التحقق من صحة الإعدادات');

        // التحقق من وجود البيانات الأساسية
        if (!currentSettings || !currentSettings.company) {
            return { isValid: false, message: 'بيانات الإعدادات غير مكتملة' };
        }

        // التحقق من اسم الشركة
        if (!currentSettings.company.companyName || !currentSettings.company.companyName.trim()) {
            return { isValid: false, message: 'اسم الشركة مطلوب' };
        }

        // التحقق من البريد الإلكتروني
        if (currentSettings.company.email && currentSettings.company.email.trim() && !isValidEmail(currentSettings.company.email)) {
            return { isValid: false, message: 'البريد الإلكتروني غير صحيح' };
        }

        // التحقق من الموقع الإلكتروني
        if (currentSettings.company.website && currentSettings.company.website.trim() && !isValidURL(currentSettings.company.website)) {
            return { isValid: false, message: 'الموقع الإلكتروني غير صحيح' };
        }

        // التحقق من معدلات الضرائب
        if (currentSettings.taxes && currentSettings.taxes.enableTax) {
            if (!currentSettings.taxes.taxRates || currentSettings.taxes.taxRates.length === 0) {
                return { isValid: false, message: 'يجب إضافة معدل ضريبة واحد على الأقل' };
            }

            const hasDefaultTax = currentSettings.taxes.taxRates.some(rate => rate.isDefault);
            if (!hasDefaultTax) {
                return { isValid: false, message: 'يجب تحديد معدل ضريبة افتراضي' };
            }
        }

        console.log('تم التحقق من صحة الإعدادات بنجاح');
        return { isValid: true };

    } catch (error) {
        console.error('خطأ في التحقق من صحة الإعدادات:', error);
        return { isValid: false, message: 'حدث خطأ في التحقق من صحة البيانات' };
    }
}

/**
 * تطبيق الإعدادات
 */
function applySettings() {
    try {
        console.log('بدء تطبيق الإعدادات:', currentSettings);

        // تطبيق الإعدادات العامة
        if (currentSettings.general) {
            // تطبيق الوضع الليلي
            if (currentSettings.general.darkMode) {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }

            // تطبيق نوع الأرقام
            document.documentElement.setAttribute('data-number-type', currentSettings.general.numberType || 'arabic');

            // تطبيق العملة
            document.documentElement.setAttribute('data-currency', currentSettings.general.currency || 'EGP');

            // تطبيق اللغة
            document.documentElement.setAttribute('lang', currentSettings.general.language || 'ar');
            document.documentElement.setAttribute('dir', 'rtl');
        }

        // تطبيق إعدادات الشركة
        if (currentSettings.company) {
            console.log('تطبيق إعدادات الشركة:', currentSettings.company);

            // تحديث اسم الشركة في جميع أنحاء التطبيق
            if (currentSettings.company.companyName) {
                const companyNameElements = document.querySelectorAll('.company-name');
                console.log('عناصر اسم الشركة الموجودة:', companyNameElements.length);

                companyNameElements.forEach(element => {
                    element.textContent = currentSettings.company.companyName;
                    console.log('تم تحديث عنصر اسم الشركة:', element);
                });

                // تحديث عنوان الصفحة
                document.title = `${currentSettings.company.companyName} - تكنوفلاش POS`;
                console.log('تم تحديث عنوان الصفحة');
            }

            // حفظ بيانات الشركة في متغير عام للوصول إليها من الملفات الأخرى
            if (typeof window !== 'undefined') {
                window.companySettings = currentSettings.company;
                console.log('تم حفظ بيانات الشركة في متغير عام');
            }
        }

        // تطبيق إعدادات نقطة البيع
        if (currentSettings.pos) {
            console.log('تطبيق إعدادات نقطة البيع:', currentSettings.pos);

            // حفظ إعدادات نقطة البيع في متغيرات عامة
            if (typeof window !== 'undefined') {
                window.posSettings = currentSettings.pos;
                window.autoSaveEnabled = currentSettings.pos.autoSave;
                window.autoPrintEnabled = currentSettings.pos.autoPrint;
                window.barcodeScannerEnabled = currentSettings.pos.barcodeScanner;
                window.lowStockAlertEnabled = currentSettings.pos.lowStockAlert;
                window.lowStockThreshold = currentSettings.pos.lowStockThreshold;
                window.invoiceNumberLength = currentSettings.pos.invoiceNumberLength;
                console.log('تم حفظ إعدادات نقطة البيع في متغيرات عامة');
            }
        }

        // إشعار باقي أجزاء التطبيق بتحديث الإعدادات
        window.dispatchEvent(new CustomEvent('settingsUpdated', {
            detail: currentSettings
        }));

        console.log('تم تطبيق الإعدادات بنجاح');

    } catch (error) {
        console.error('خطأ في تطبيق الإعدادات:', error);
    }
}

/**
 * إعادة تعيين الإعدادات
 */
function resetSettings() {
    const confirmMessage = 'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n\nسيتم حذف جميع الإعدادات المخصصة واستخدام الإعدادات الافتراضية (الجنيه المصري كعملة وحيدة).';

    if (typeof app !== 'undefined' && app && app.showConfirm) {
        app.showConfirm(
            'إعادة تعيين الإعدادات',
            confirmMessage,
            () => {
                if (emergencySettingsReset()) {
                    // إعادة تحميل الصفحة بعد نجاح العملية
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            }
        );
    } else {
        if (confirm(confirmMessage)) {
            if (emergencySettingsReset()) {
                // إعادة تحميل الصفحة بعد نجاح العملية
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }
    }
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من صحة الرابط
 */
function isValidURL(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * معاينة الفاتورة
 */
function previewInvoice() {
    // جمع إعدادات الفاتورة الحالية
    const invoiceSettings = collectInvoiceSettings();

    // إنشاء بيانات تجريبية للمعاينة
    const sampleData = {
        id: 'PREVIEW-001',
        invoiceNumber: 'INV-2024-001',
        date: new Date().toISOString(),
        customerId: 'sample',
        customer: {
            name: 'عميل تجريبي',
            phone: '0501234567',
            address: 'الرياض، المملكة العربية السعودية'
        },
        items: [
            {
                productId: '1',
                name: 'منتج تجريبي 1',
                description: 'وصف المنتج التجريبي الأول',
                price: 100,
                quantity: 2,
                total: 200
            },
            {
                productId: '2',
                name: 'منتج تجريبي 2',
                description: 'وصف المنتج التجريبي الثاني',
                price: 50,
                quantity: 1,
                total: 50
            }
        ],
        subtotal: 250,
        discount: 5,
        discountAmount: 12.5,
        tax: 15,
        taxAmount: 35.63,
        total: 273.13,
        paymentMethod: 'cash'
    };

    // إنشاء نافذة المعاينة
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    const invoiceHTML = generateInvoiceHTML(sampleData, invoiceSettings);

    if (previewWindow) {
        previewWindow.document.open();
        previewWindow.document.write(invoiceHTML);
        previewWindow.document.close();
    } else {
        alert('تعذر فتح نافذة المعاينة. تأكد من السماح للنوافذ المنبثقة.');
    }
}

/**
 * جمع إعدادات الفاتورة من النموذج
 */
function collectInvoiceSettings() {
    const form = document.getElementById('invoiceSettingsForm');
    const formData = new FormData(form);

    return {
        // الشكل والحجم
        paperSize: formData.get('paperSize') || 'A4',
        customWidth: parseInt(formData.get('customWidth')) || 210,
        customHeight: parseInt(formData.get('customHeight')) || 297,
        orientation: formData.get('orientation') || 'portrait',
        invoiceTemplate: formData.get('invoiceTemplate') || 'classic',
        headerColor: formData.get('headerColor') || '#2c3e50',
        textColor: formData.get('textColor') || '#333333',

        // المحتوى
        showLogo: form.querySelector('[name="showLogo"]')?.checked || false,
        showCompanyInfo: form.querySelector('[name="showCompanyInfo"]')?.checked || false,
        showCustomerInfo: form.querySelector('[name="showCustomerInfo"]')?.checked || false,
        showItemCode: form.querySelector('[name="showItemCode"]')?.checked || false,
        showItemDescription: form.querySelector('[name="showItemDescription"]')?.checked || false,
        showUnitPrice: form.querySelector('[name="showUnitPrice"]')?.checked || false,
        showDiscount: form.querySelector('[name="showDiscount"]')?.checked || false,
        showTax: form.querySelector('[name="showTax"]')?.checked || false,
        showQRCode: form.querySelector('[name="showQRCode"]')?.checked || false,
        showBarcode: form.querySelector('[name="showBarcode"]')?.checked || false,

        // النصوص المخصصة
        invoiceTitle: formData.get('invoiceTitle') || 'فاتورة مبيعات',
        thankYouMessage: formData.get('thankYouMessage') || '',
        termsConditions: formData.get('termsConditions') || '',
        footerNotes: formData.get('footerNotes') || '',

        // الطباعة
        marginTop: parseInt(formData.get('marginTop')) || 10,
        marginRight: parseInt(formData.get('marginRight')) || 10,
        marginBottom: parseInt(formData.get('marginBottom')) || 10,
        marginLeft: parseInt(formData.get('marginLeft')) || 10,
        fontSize: parseInt(formData.get('fontSize')) || 12,
        fontFamily: formData.get('fontFamily') || 'Cairo',
        printCopies: parseInt(formData.get('printCopies')) || 1
    };
}

/**
 * إنشاء HTML للفاتورة
 */
function generateInvoiceHTML(saleData, settings) {
    const companySettings = currentSettings.company || {};

    return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${settings.invoiceTitle}</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: '${settings.fontFamily}', Arial, sans-serif;
                font-size: ${settings.fontSize}px;
                color: ${settings.textColor};
                line-height: 1.6;
                background: white;
                padding: ${settings.marginTop}mm ${settings.marginRight}mm ${settings.marginBottom}mm ${settings.marginLeft}mm;
            }

            .invoice-container {
                max-width: 100%;
                margin: 0 auto;
            }

            .invoice-header {
                background: ${settings.headerColor};
                color: white;
                padding: 20px;
                border-radius: 8px 8px 0 0;
                margin-bottom: 20px;
            }

            .company-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .company-details h1 {
                font-size: 24px;
                margin-bottom: 10px;
            }

            .invoice-info {
                text-align: left;
            }

            .invoice-details {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 5px;
            }

            .customer-info, .invoice-meta {
                padding: 10px;
            }

            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }

            .items-table th,
            .items-table td {
                padding: 12px;
                text-align: center;
                border: 1px solid #ddd;
            }

            .items-table th {
                background: ${settings.headerColor};
                color: white;
                font-weight: 600;
            }

            .items-table tbody tr:nth-child(even) {
                background: #f8f9fa;
            }

            .totals-section {
                display: flex;
                justify-content: flex-end;
                margin-bottom: 20px;
            }

            .totals-table {
                width: 300px;
                border-collapse: collapse;
            }

            .totals-table td {
                padding: 8px 15px;
                border: 1px solid #ddd;
            }

            .totals-table .total-row {
                background: ${settings.headerColor};
                color: white;
                font-weight: bold;
            }

            .footer-section {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 2px solid ${settings.headerColor};
            }

            .thank-you {
                text-align: center;
                font-size: 16px;
                color: ${settings.headerColor};
                margin-bottom: 15px;
            }

            .terms {
                font-size: 10px;
                color: #666;
                margin-bottom: 10px;
            }

            .footer-notes {
                text-align: center;
                font-size: 11px;
                color: #888;
            }

            @media print {
                body { margin: 0; }
                .invoice-container { box-shadow: none; }
            }
        </style>
    </head>
    <body>
        <div class="invoice-container">
            ${settings.showLogo || settings.showCompanyInfo ? `
            <div class="invoice-header">
                <div class="company-info">
                    <div class="company-details">
                        ${settings.showCompanyInfo ? `
                        <h1>${companySettings.companyName || 'اسم الشركة'}</h1>
                        <p>${companySettings.address || 'عنوان الشركة'}</p>
                        <p>هاتف: ${companySettings.phone || '0123456789'}</p>
                        <p>بريد: ${companySettings.email || '<EMAIL>'}</p>
                        ${companySettings.taxNumber ? `<p>الرقم الضريبي: ${companySettings.taxNumber}</p>` : ''}
                        ${companySettings.commercialRegister ? `<p>السجل التجاري: ${companySettings.commercialRegister}</p>` : ''}
                        ` : ''}
                    </div>
                    <div class="invoice-info">
                        <h2>${settings.invoiceTitle}</h2>
                        <p>رقم الفاتورة: ${saleData.invoiceNumber}</p>
                        <p>التاريخ: ${db.formatDate ? db.formatDate(saleData.date) : new Date(saleData.date).toLocaleDateString('ar-SA')}</p>
                    </div>
                </div>
            </div>
            ` : ''}

            <div class="invoice-details">
                ${settings.showCustomerInfo ? `
                <div class="customer-info">
                    <h3>بيانات العميل</h3>
                    <p><strong>الاسم:</strong> ${saleData.customer?.name || 'عميل نقدي'}</p>
                    ${saleData.customer?.phone ? `<p><strong>الهاتف:</strong> ${saleData.customer.phone}</p>` : ''}
                    ${saleData.customer?.address ? `<p><strong>العنوان:</strong> ${saleData.customer.address}</p>` : ''}
                </div>
                ` : ''}

                <div class="invoice-meta">
                    <h3>تفاصيل الفاتورة</h3>
                    <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(saleData.paymentMethod)}</p>
                    <p><strong>عدد الأصناف:</strong> ${saleData.items.length}</p>
                </div>
            </div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th>#</th>
                        ${settings.showItemCode ? '<th>الكود</th>' : ''}
                        <th>اسم المنتج</th>
                        ${settings.showItemDescription ? '<th>الوصف</th>' : ''}
                        <th>الكمية</th>
                        ${settings.showUnitPrice ? '<th>سعر الوحدة</th>' : ''}
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${saleData.items.map((item, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            ${settings.showItemCode ? `<td>${item.productId}</td>` : ''}
                            <td>${item.name}</td>
                            ${settings.showItemDescription ? `<td>${item.description || '-'}</td>` : ''}
                            <td>${db.toArabicNumerals ? db.toArabicNumerals(item.quantity) : item.quantity}</td>
                            ${settings.showUnitPrice ? `<td>${db.formatCurrency ? db.formatCurrency(item.price) : item.price}</td>` : ''}
                            <td>${db.formatCurrency ? db.formatCurrency(item.total) : item.total}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="totals-section">
                <table class="totals-table">
                    <tr>
                        <td>المجموع الفرعي:</td>
                        <td>${db.formatCurrency ? db.formatCurrency(saleData.subtotal) : saleData.subtotal}</td>
                    </tr>
                    ${settings.showDiscount && saleData.discountAmount > 0 ? `
                    <tr>
                        <td>الخصم (${saleData.discount}%):</td>
                        <td>-${db.formatCurrency ? db.formatCurrency(saleData.discountAmount) : saleData.discountAmount}</td>
                    </tr>
                    ` : ''}
                    ${settings.showTax && saleData.taxAmount > 0 ? `
                    <tr>
                        <td>الضريبة (${saleData.tax}%):</td>
                        <td>${db.formatCurrency ? db.formatCurrency(saleData.taxAmount) : saleData.taxAmount}</td>
                    </tr>
                    ` : ''}
                    <tr class="total-row">
                        <td>الإجمالي النهائي:</td>
                        <td>${db.formatCurrency ? db.formatCurrency(saleData.total) : saleData.total}</td>
                    </tr>
                </table>
            </div>

            <div class="footer-section">
                ${settings.thankYouMessage ? `<div class="thank-you">${settings.thankYouMessage}</div>` : ''}
                ${settings.termsConditions ? `<div class="terms"><strong>الشروط والأحكام:</strong><br>${settings.termsConditions}</div>` : ''}
                ${settings.footerNotes ? `<div class="footer-notes">${settings.footerNotes}</div>` : ''}
            </div>
        </div>
    </body>
    </html>
    `;
}

/**
 * تحديث معاينة الفاتورة عند تغيير الإعدادات
 */
function updateInvoicePreview() {
    // يمكن إضافة معاينة مباشرة هنا إذا لزم الأمر
    console.log('تم تحديث إعدادات الفاتورة');
}

/**
 * التعامل مع تغيير حجم الورق
 */
function handlePaperSizeChange() {
    const paperSize = document.querySelector('[name="paperSize"]').value;
    const customSizeGroup = document.getElementById('customSizeGroup');

    if (paperSize === 'custom') {
        customSizeGroup.style.display = 'block';
    } else {
        customSizeGroup.style.display = 'none';
    }
}

/**
 * الحصول على نص طريقة الدفع
 */
function getPaymentMethodText(method) {
    const methods = {
        'cash': 'نقداً',
        'card': 'بطاقة',
        'transfer': 'تحويل',
        'credit': 'آجل'
    };
    return methods[method] || method;
}

/**
 * دوال مساعدة للتشخيص (يمكن استخدامها من وحدة التحكم)
 */
window.settingsDebug = {
    // عرض الإعدادات الحالية
    showCurrentSettings: () => {
        console.log('الإعدادات الحالية:', currentSettings);
        return currentSettings;
    },

    // عرض بيانات localStorage
    showLocalStorage: () => {
        const data = localStorage.getItem('technoflash_settings');
        console.log('بيانات localStorage:', data);
        if (data) {
            try {
                const parsed = JSON.parse(data);
                console.log('البيانات المحللة:', parsed);
                return parsed;
            } catch (e) {
                console.error('خطأ في تحليل البيانات:', e);
                return null;
            }
        }
        return null;
    },

    // تشغيل التشخيص
    runDiagnostic: () => {
        return debugSettingsLoad();
    },

    // إعادة تعيين طارئة
    emergencyReset: () => {
        return emergencySettingsReset();
    },

    // إعادة تحميل الإعدادات
    reloadSettings: () => {
        try {
            loadCurrentSettings();
            populateSettingsForms();
            applySettings();
            console.log('✅ تم إعادة تحميل الإعدادات بنجاح');
            return true;
        } catch (error) {
            console.error('❌ خطأ في إعادة تحميل الإعدادات:', error);
            return false;
        }
    }
};

// إضافة رسالة ترحيب للمطورين
console.log('🔧 أدوات تشخيص الإعدادات متاحة في: window.settingsDebug');
console.log('📖 الأوامر المتاحة:');
console.log('  - settingsDebug.showCurrentSettings() // عرض الإعدادات الحالية');
console.log('  - settingsDebug.showLocalStorage() // عرض بيانات التخزين المحلي');
console.log('  - settingsDebug.runDiagnostic() // تشغيل التشخيص');
console.log('  - settingsDebug.emergencyReset() // إعادة تعيين طارئة');
console.log('  - settingsDebug.reloadSettings() // إعادة تحميل الإعدادات');
